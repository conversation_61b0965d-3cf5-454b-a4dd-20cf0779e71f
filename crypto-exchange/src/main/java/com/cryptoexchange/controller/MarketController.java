package com.cryptoexchange.controller;

import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.service.MarketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * 市场数据控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "市场数据", description = "行情数据、K线图表、交易对信息等相关接口")
@RestController
@RequestMapping("/api/market")
@RequiredArgsConstructor
@Validated
public class MarketController {

    private final MarketService marketService;

    @Operation(summary = "获取所有交易对", description = "获取平台支持的所有交易对信息")
    @GetMapping("/symbols")
    public Result<List<SymbolResponse>> getSymbols(
            @RequestParam(required = false) String baseAsset,
            @RequestParam(required = false) String quoteAsset,
            @RequestParam(required = false) String status) {
        
        SymbolQueryRequest request = new SymbolQueryRequest();
        request.setBaseAsset(baseAsset);
        request.setQuoteAsset(quoteAsset);
        request.setStatus(status);
        
        List<SymbolResponse> response = marketService.getSymbols(request);
        return Result.success(response);
    }

    @Operation(summary = "获取交易对详情", description = "获取指定交易对的详细信息")
    @GetMapping("/symbols/{symbol}")
    public Result<SymbolDetailResponse> getSymbolDetail(
            @PathVariable @NotBlank(message = "交易对代码不能为空") String symbol) {
        SymbolDetailResponse response = marketService.getSymbolDetail(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取市场深度", description = "获取指定交易对的市场深度信息")
    @GetMapping("/depth")
    public Result<MarketDepthResponse> getMarketDepth(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol,
            @RequestParam(defaultValue = "20") @Positive(message = "深度级别必须大于0") Integer limit) {
        MarketDepthResponse response = marketService.getMarketDepth(symbol, limit);
        return Result.success(response);
    }

    @Operation(summary = "获取最新成交", description = "获取指定交易对的最新成交记录")
    @GetMapping("/trades")
    public Result<List<RecentTradeResponse>> getRecentTrades(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol,
            @RequestParam(defaultValue = "50") @Positive(message = "记录数量必须大于0") Integer limit) {
        List<RecentTradeResponse> response = marketService.getRecentTrades(symbol, limit);
        return Result.success(response);
    }

    @Operation(summary = "获取24小时行情", description = "获取指定交易对或所有交易对的24小时行情统计")
    @GetMapping("/ticker/24hr")
    public Result<List<Ticker24hrResponse>> get24hrTicker(
            @RequestParam(required = false) String symbol) {
        List<Ticker24hrResponse> response = marketService.get24hrTicker(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取当前价格", description = "获取指定交易对或所有交易对的当前价格")
    @GetMapping("/ticker/price")
    public Result<List<TickerPriceResponse>> getTickerPrice(
            @RequestParam(required = false) String symbol) {
        List<TickerPriceResponse> response = marketService.getTickerPrice(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取最优挂单", description = "获取指定交易对或所有交易对的最优买卖挂单")
    @GetMapping("/ticker/book-ticker")
    public Result<List<BookTickerResponse>> getBookTicker(
            @RequestParam(required = false) String symbol) {
        List<BookTickerResponse> response = marketService.getBookTicker(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取K线数据", description = "获取指定交易对的K线数据")
    @GetMapping("/klines")
    public Result<List<KlineResponse>> getKlines(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol,
            @RequestParam @NotBlank(message = "时间间隔不能为空") String interval,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "500") @Positive(message = "记录数量必须大于0") Integer limit) {
        
        KlineQueryRequest request = new KlineQueryRequest();
        request.setSymbol(symbol);
        request.setInterval(interval);
        // 注意：startTime和endTime字段是LocalDateTime类型，这里暂时不设置
        // 如果需要设置时间，应该将String转换为LocalDateTime
        request.setLimit(limit);
        
        List<KlineResponse> response = marketService.getKlines(request);
        return Result.success(response);
    }

    @Operation(summary = "获取平均价格", description = "获取指定交易对的平均价格")
    @GetMapping("/avg-price")
    public Result<AvgPriceResponse> getAvgPrice(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol) {
        AvgPriceResponse response = marketService.getAvgPrice(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取交易统计", description = "获取指定交易对的交易统计信息")
    @GetMapping("/stats")
    public Result<TradingStatsResponse> getTradingStats(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol,
            @RequestParam(required = false) String period) {
        TradingStatsResponse response = marketService.getTradingStats(symbol, period);
        return Result.success(response);
    }

    @Operation(summary = "获取历史交易", description = "获取指定交易对的历史交易记录")
    @GetMapping("/historical-trades")
    public Result<List<HistoricalTradeResponse>> getHistoricalTrades(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol,
            @RequestParam(required = false) Long fromId,
            @RequestParam(defaultValue = "500") @Positive(message = "记录数量必须大于0") Integer limit) {
        
        HistoricalTradeQueryRequest request = new HistoricalTradeQueryRequest();
        request.setSymbol(symbol);
        request.setFromId(fromId);
        request.setLimit(limit);
        
        List<HistoricalTradeResponse> response = marketService.getHistoricalTrades(request);
        return Result.success(response);
    }

    @Operation(summary = "获取聚合交易", description = "获取指定交易对的聚合交易记录")
    @GetMapping("/agg-trades")
    public Result<List<AggTradeResponse>> getAggTrades(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol,
            @RequestParam(required = false) Long fromId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "500") @Positive(message = "记录数量必须大于0") Integer limit) {
        
        AggTradeQueryRequest request = new AggTradeQueryRequest();
        request.setSymbol(symbol);
        request.setFromId(fromId);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setLimit(limit);
        
        List<AggTradeResponse> response = marketService.getAggTrades(request);
        return Result.success(response);
    }

    @Operation(summary = "获取市场概览", description = "获取整个市场的概览信息")
    @GetMapping("/overview")
    public Result<MarketOverviewResponse> getMarketOverview() {
        MarketOverviewResponse response = marketService.getMarketOverview();
        return Result.success(response);
    }

    @Operation(summary = "获取热门交易对", description = "获取热门交易对列表")
    @GetMapping("/hot-symbols")
    public Result<List<HotSymbolResponse>> getHotSymbols(
            @RequestParam(defaultValue = "10") @Positive(message = "记录数量必须大于0") Integer limit) {
        List<HotSymbolResponse> response = marketService.getHotSymbols(limit);
        return Result.success(response);
    }

    @Operation(summary = "获取涨跌幅排行", description = "获取涨跌幅排行榜")
    @GetMapping("/gainers-losers")
    public Result<GainersLosersResponse> getGainersLosers(
            @RequestParam(defaultValue = "10") @Positive(message = "记录数量必须大于0") Integer limit) {
        GainersLosersResponse response = marketService.getGainersLosers(limit);
        return Result.success(response);
    }

    @Operation(summary = "获取成交量排行", description = "获取成交量排行榜")
    @GetMapping("/volume-ranking")
    public Result<List<VolumeRankingResponse>> getVolumeRanking(
            @RequestParam(defaultValue = "10") @Positive(message = "记录数量必须大于0") Integer limit,
            @RequestParam(defaultValue = "24h") String period) {
        List<VolumeRankingResponse> response = marketService.getVolumeRanking(limit, period);
        return Result.success(response);
    }

    @Operation(summary = "获取新币上线", description = "获取新上线的交易对")
    @GetMapping("/new-listings")
    public Result<List<NewListingResponse>> getNewListings(
            @RequestParam(defaultValue = "10") @Positive(message = "记录数量必须大于0") Integer limit) {
        List<NewListingResponse> response = marketService.getNewListings(limit);
        return Result.success(response);
    }

    @Operation(summary = "获取市场分类", description = "获取市场分类信息")
    @GetMapping("/categories")
    public Result<List<MarketCategoryResponse>> getMarketCategories() {
        List<MarketCategoryResponse> response = marketService.getMarketCategories();
        return Result.success(response);
    }

    @Operation(summary = "按分类获取交易对", description = "根据分类获取交易对列表")
    @GetMapping("/categories/{categoryId}/symbols")
    public Result<List<SymbolResponse>> getSymbolsByCategory(
            @PathVariable @NotBlank(message = "分类ID不能为空") String categoryId) {
        List<SymbolResponse> response = marketService.getSymbolsByCategory(categoryId);
        return Result.success(response);
    }

    @Operation(summary = "搜索交易对", description = "根据关键词搜索交易对")
    @GetMapping("/search")
    public Result<List<SymbolSearchResponse>> searchSymbols(
            @RequestParam @NotBlank(message = "搜索关键词不能为空") String keyword,
            @RequestParam(defaultValue = "20") @Positive(message = "记录数量必须大于0") Integer limit) {
        List<SymbolSearchResponse> response = marketService.searchSymbols(keyword, limit);
        return Result.success(response);
    }

    @Operation(summary = "获取交易对状态", description = "获取交易对的交易状态")
    @GetMapping("/symbol-status")
    public Result<List<SymbolStatusResponse>> getSymbolStatus(
            @RequestParam(required = false) String symbol) {
        List<SymbolStatusResponse> response = marketService.getSymbolStatus(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取服务器时间", description = "获取服务器当前时间")
    @GetMapping("/time")
    public Result<ServerTimeResponse> getServerTime() {
        ServerTimeResponse response = marketService.getServerTime();
        return Result.success(response);
    }

    @Operation(summary = "获取汇率信息", description = "获取法币汇率信息")
    @GetMapping("/exchange-rates")
    public Result<List<ExchangeRateResponse>> getExchangeRates(
            @RequestParam(required = false) String baseCurrency,
            @RequestParam(required = false) String targetCurrency) {
        
        ExchangeRateQueryRequest request = new ExchangeRateQueryRequest();
        request.setBaseCurrency(baseCurrency);
        request.setTargetCurrency(targetCurrency);
        
        List<ExchangeRateResponse> response = marketService.getExchangeRates(request);
        return Result.success(response);
    }

    @Operation(summary = "获取市场公告", description = "获取市场相关公告")
    @GetMapping("/announcements")
    public Result<List<MarketAnnouncementResponse>> getMarketAnnouncements(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "10") @Positive(message = "记录数量必须大于0") Integer limit) {
        List<MarketAnnouncementResponse> response = marketService.getMarketAnnouncements(type, limit);
        return Result.success(response);
    }

    @Operation(summary = "获取系统状态", description = "获取交易系统状态")
    @GetMapping("/system-status")
    public Result<SystemStatusResponse> getSystemStatus() {
        SystemStatusResponse response = marketService.getSystemStatus();
        return Result.success(response);
    }

    @Operation(summary = "获取API限制信息", description = "获取API调用限制信息")
    @GetMapping("/api-limits")
    public Result<ApiLimitsResponse> getApiLimits() {
        ApiLimitsResponse response = marketService.getApiLimits();
        return Result.success(response);
    }

    @Operation(summary = "获取历史K线", description = "获取历史K线数据（支持更大时间范围）")
    @GetMapping("/historical-klines")
    public Result<List<KlineResponse>> getHistoricalKlines(
            @RequestParam @NotBlank(message = "交易对代码不能为空") String symbol,
            @RequestParam @NotBlank(message = "时间间隔不能为空") String interval,
            @RequestParam @NotBlank(message = "开始时间不能为空") String startTime,
            @RequestParam @NotBlank(message = "结束时间不能为空") String endTime) {
        
        HistoricalKlineQueryRequest request = new HistoricalKlineQueryRequest();
        request.setSymbol(symbol);
        request.setInterval(interval);
        // 注意：startTime和endTime字段是LocalDateTime类型，这里暂时不设置
        // 如果需要设置时间，应该将String转换为LocalDateTime
        
        List<KlineResponse> response = marketService.getHistoricalKlines(request);
        return Result.success(response);
    }

    @Operation(summary = "获取价格变化统计", description = "获取价格变化统计信息")
    @GetMapping("/price-change-stats")
    public Result<List<PriceChangeStatsResponse>> getPriceChangeStats(
            @RequestParam(required = false) String symbol,
            @RequestParam(defaultValue = "24h") String period) {
        List<PriceChangeStatsResponse> response = marketService.getPriceChangeStats(symbol, period);
        return Result.success(response);
    }
}